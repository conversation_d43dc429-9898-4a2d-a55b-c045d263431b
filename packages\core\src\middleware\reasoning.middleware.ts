import { Context, Logger } from "koishi";

import { DataManager } from "../services/worldstate/DataManager";
import { Action, ActionResult, AgentResponse } from "../services/worldstate/interfaces";
import { extractJSONFromString } from "../utils/parse-structured-output";
import { AgentContext } from "../agent/context";
import { Middleware } from ".";


interface FunctionTool {
    function: string;
    params: Record<string, unknown>;
}

interface OutputFormat {
    thoughts: {
        observe: string;
        analyze_infer: string;
        plan: string;
    };
    actions: FunctionTool[];
    request_heartbeat: boolean;
}

export class ReasoningMiddleware extends Middleware<AgentContext> {
    // 默认配置常量
    private static readonly DEFAULT_MAX_RETRY = 3;
    private static readonly DEFAULT_LIFE = 3;
    private static readonly DEFAULT_MAX_HEARTBEAT = 5;
    private static readonly RETRY_DELAY_MS = 1500;

    private readonly logger: Logger;
    private readonly dataManager: DataManager;

    constructor(
        protected ctx: Context,
        protected config: {
            maxRetry: number;
            life: number;
            maxHeartbeat?: number;
        }
    ) {
        super("response-handling", ctx);
        this.logger = ctx.logger("ResponseHandling");
        this.dataManager = ctx["yesimbot.data"];
    }

    async execute(agentCtx: AgentContext, next: () => Promise<void>): Promise<void> {
    }
}
