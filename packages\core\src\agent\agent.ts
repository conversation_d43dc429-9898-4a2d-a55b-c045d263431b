import { clone, Context, Session } from "koishi";
import { Config } from "../config";
import { MiddlewareContext, Pipeline } from "../middleware";
import { ErrorHandlingMiddleware } from "../middleware/error.middleware";
import { ReplyConditionMiddleware } from "../middleware/condition.middleware";

export default class AgentCore {
    private ctx: Context;
    private config: Config;

    private pipeline: Pipeline;

    constructor(ctx: Context, config: Config) {
        this.ctx = ctx;
        this.config = config;

        this.pipeline = new Pipeline();

        ctx.on("ready", async () => {
            this.pipeline.use(
                new ErrorHandlingMiddleware(ctx, {
                    Debug: this.config.Debug.EnableDebug,
                    UploadDump: this.config.Debug.UploadDump,
                    PasteServiceUrl: "https://dump.yesimbot.chat/",
                    IncludeFullSessionContent: false,
                })
            );

            this.pipeline.use(new ReplyConditionMiddleware(ctx, this.config.ReplyCondition));

            ctx.middleware(async (session, next) => {
                try {
                    const context = await MiddlewareContext.create(this.ctx, session, this.config.ReplyCondition.AllowedChannels);
                    await this.pipeline.execute(context);
                } catch (error) {
                    this.ctx.logger.error("[Agent] 消息处理错误:", (error as Error).message);
                    if (this.config.Debug.EnableDebug) {
                        this.ctx.logger.error((error as Error).stack);
                    }
                }
                return next();
            });
        });
    }
}
