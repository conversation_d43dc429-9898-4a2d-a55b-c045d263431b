import { Context } from "koishi";
import { Middleware } from ".";
import { AgentContext } from "../agent/context";
import { IServiceContainer } from "../services/ServiceContainer";

export interface DatabaseStorageConfig {
    EnableMessageStorage?: boolean;
    EnableImageProcessing?: boolean;
    EnableWorldStateUpdate?: boolean;
}

/**
 * 数据库存储中间件
 */
export class DatabaseStorageMiddleware extends Middleware<AgentContext> {
    constructor(protected ctx: Context, protected services: IServiceContainer, protected config: DatabaseStorageConfig) {
        super("database-storage", ctx, services, config);
    }

    async execute(agentCtx: AgentContext, next: () => Promise<void>): Promise<void> {}
}
